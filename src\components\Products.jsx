// import data from "../data.json"


const Products = () => {

    const data = [
  {
    "id": 1,
    "img": "https://media.istockphoto.com/id/2053961830/photo/luxury-modern-gray-kitchen.jpg?s=2048x2048&w=is&k=20&c=925kArV051VRMueQMsZM5omDf8XeybQKaBZ1RKHD7qw=",
    "name": "Kitchen",
    "description": "A comfortable and stylish sofa for your living room."
  },
  {
    "id": 4,
    "img": "https://media.istockphoto.com/id/2218549634/photo/a-white-beautiful-stylish-chair-stands-near-a-large-floor-to-ceiling-window-light-curtains.jpg?s=2048x2048&w=is&k=20&c=mg0tWB508cks-jtEyII1lQOqhMpGwmLC_H-iHTN0hnI=",
    "name": "Curtains",
    "description": "Get your furniture reupholstered with top-notch fabrics."
  },
  {
    "id": 2,
    "img": "https://media.istockphoto.com/id/1333060261/photo/modern-living-room-interior-design.jpg?s=2048x2048&w=is&k=20&c=kd9D4Czh0ldEr1kC_Dj2sz5qUgWwrWLnpUqZOUks6hg=",
    "name": "Sofa Upholstery",
    "description": "A modular kitchen setup with smart storage and lighting."
},
  {
    "id": 3,
    "img": "https://media.istockphoto.com/id/166628959/photo/smiling-woman-looking-out-window.jpg?s=2048x2048&w=is&k=20&c=IU4-gdGtLlRYqAwwsMOhXUW-2patdfCEM5-QAKG4thg=",
    "name": "Window Blinds",
    "description": "A modular kitchen setup with smart storage and lighting."
}
]


    return (
        <div>
            <section className='text-center mt-10 mb-8 md:mb-12'>
                <h1 className='text-3xl md:text-5xl font-bold'>
                    Explore our interior design solutions
                </h1>                   
            </section>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-8">
                {data.map((item) => (
                    <div
                        key={item.id}
                        className="bg-white  rounded-xl overflow-hidden shadow-md  hover:shadow-xl"
                    >
                        <img src={item.img} alt={item.title} className="w-full h-100 object-cover transform transition-transform duration-300 hover:scale-105" />
                        <div className="p-4">
                            <h2 className="text-xl font-semibold mb-2 text-gray-800">{item.name}</h2>
                            <p className="text-gray-600">{item.description}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default Products