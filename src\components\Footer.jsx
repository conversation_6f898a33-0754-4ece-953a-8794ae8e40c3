import React from "react";
import {
  <PERSON>aFacebookF,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ram,
  FaLinkedinIn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaWhatsapp,
} from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-[#231F20] text-gray-200 pt-10 pb-6 px-6 md:px-16">
      {/* Main Sections */}
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row gap-10 md:gap-8 justify-between">
        {/* Logo and Social */}
        <div>
          <h2 className="text-3xl font-bold mb-4 tracking-wide">
            Vasu Interio
          </h2>
          <div className="flex gap-4 mb-6">
            <a href="/" aria-label="Instagram" className="hover:text-pink-400"><FaInstagram size={20}/></a>
          </div>
        </div>

        {/* Products & Bedroom Sets */}
        <div>
          <h4 className="text-base font-semibold mb-2 tracking-wider">PRODUCTS</h4>
          <ul className="text-sm leading-8">
            <li><a href="#" className="hover:text-white">Modular Kitchens</a></li>
            <li><a href="#" className="hover:text-white">Upholstery</a></li>
            <li><a href="#" className="hover:text-white">Curtains & Blinds</a></li>
          </ul>
        </div>

        

        {/* Address & Contacts */}
        <div>
          <h4 className="text-base font-semibold mb-2 tracking-wider">ADDRESS</h4>
          <p className="text-sm text-gray-400 mb-2 uppercase">
            10-lakkar mandi,<br />
            Sri ganganagar 335001, <br/> rajasthan, India.
          </p>
          <h4 className="text-base font-semibold mb-1 mt-4">CONTACT US</h4>
         
          <p className="text-sm mb-2">+91 9876543210</p>
          <h4 className="text-base font-semibold mb-1 mt-4">E-MAIL ID</h4>
          <p className="text-sm"><EMAIL></p>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-700 mt-12 pt-4 flex flex-col md:flex-row justify-between text-xs text-gray-400">
        <div>
          <a href="#" className="hover:text-white">Privacy Policy</a> | <a href="#" className="hover:text-white">Terms & Condition</a>
        </div>
        <div>
          Copyright © 2025 Vasu Interio
        </div>
      </div>
    </footer>
  );
};

export default Footer;
