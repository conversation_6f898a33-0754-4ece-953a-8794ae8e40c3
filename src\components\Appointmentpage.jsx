import React, { useState } from "react";

const AppointmentForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    pincode: "",
    phone: "",
    email: "",
    whatsappOptIn: false,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Appointment Form Data:", formData);
    alert("Appointment request submitted!");
    // Optionally: send to server or email
  };

  return (
    <div className="bg-gradient-to-r from-purple-100 to-orange-100 p-20 rounded-3xl shadow-lg max-w-6xl mx-auto my-10">
      <div className="grid md:grid-cols-2 gap-8 bg-transparent ">
        <div className=" text-center">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900">
            Make your  <br />
           Home 🏠 Beautiful  <br />
            by Vasu Interio
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input
              type="text"
              name="name"
              placeholder="Enter your name"
              className="border text-2xl rounded-md px-4 py-4 w-full"
              value={formData.name}
              onChange={handleChange}
              required
            />
            <input
              type="text"
              name="pincode"
              placeholder="Enter your Pincode"
              className="border text-2xl rounded-md px-4 py-4 w-full"
              value={formData.pincode}
              onChange={handleChange}
              required
            />
            <div className="flex items-center border rounded-md px-2">
              <div className="text-sm px-3">🇮🇳 +91</div>
              
              <input
                type="tel"
                name="phone"
                placeholder="Enter mobile number"
                className="border text-2xl rounded-md px-4 py-4 w-full outline-none"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </div>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              className="border rounded-md px-4 py-2 w-full"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>

          <p className="text-xs text-gray-500">
            By proceeding, you are authorizing Vasu Interio to get in touch with you through calls, SMS, or e-mail.
          </p>

           <button className="bg-gradient-to-r from-teal-400 to-blue-500 hover:from-blue-500 hover:to-teal-400 text-white px-4 py-2 rounded-full font-semibold shadow-md transition duration-300">
              Book an Appointment
            </button>
        </form>
      </div>
    </div>
  );
};

export default AppointmentForm;
