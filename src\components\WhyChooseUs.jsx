import React from 'react'
import { FaThumbsUp, FaRegSmile, FaAward, FaRegClock } from "react-icons/fa";




const WhyChooseUs = () => {

    const reasons = [
        {
            icon: <FaAward className="text-teal-500 text-3xl mb-2" />,
            title: "Quality Craftsmanship",
            description: "We deliver the highest standards in every project, using top quality materials and skilled workmanship."
        },
        {
            icon: <FaThumbsUp className="text-blue-500 text-3xl mb-2" />,
            title: "Customer Satisfaction",
            description: "We focus on building lasting relationships and guarantee you a seamless, enjoyable experience."
        },
        {
            icon: <FaRegClock className="text-violet-500 text-3xl mb-2" />,
            title: "On Time Completion",
            description: "Our friendly team guides you at every step, providing expert advice tailored to your needs."
        },
        {
            icon: <FaRegSmile className="text-yellow-500 text-3xl mb-2" />,
            title: "Proven Reliability",
            description: "With years of trusted service, our clients return and refer us for our reliability and integrity."
        }
    ];


    return (
        <section className='bg-slate-600 p-10' >
            <div className='text-center text-white mt-10 mb-8 md:mb-12'>
                <h1 className='text-3xl md:text-5xl font-bold mb-4'>Why Choose Us?</h1>
                <p className="mb-12 text-gray-300 max-w-2xl mx-auto">
                    Experience the difference with Vasu Interio. We combine creativity, reliability, and customer-centric values to bring your dream space to life.
                </p>
            </div>

            {/* <section className="py-16 px-6 bg-gray-50" id="why-choose-us"> */}
            <div className="max-w-4xl mx-auto text-center">


                <div className="flex flex-col md:flex-row md:flex-nowrap md:space-x-4">
  {reasons.map((reason, idx) => (
    <div
      key={idx}
      className="bg-white rounded-xl shadow p-6 flex flex-col items-center transition hover:shadow-lg mb-6 md:mb-0 md:flex-1"
    >
      {reason.icon}
      <h3 className="text-xl font-semibold mb-2 text-gray-700">{reason.title}</h3>
      <p className="text-gray-500">{reason.description}</p>
    </div>
  ))}
</div>


            </div>
           

        </section>
    )
}

export default WhyChooseUs