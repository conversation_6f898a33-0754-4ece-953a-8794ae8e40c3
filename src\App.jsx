import './App.css'
import AppointmentForm from './components/Appointmentpage'
import Features from './components/Features'
import Footer from './components/Footer'
import Hero from './components/Hero'
import Navbar from './components/Navbar'
import Products from './components/Products'
import WhyChooseUs from './components/WhyChooseUs'
import { createBrowserRouter } from 'react-router-dom'


const router = createBrowserRouter([
  {path:"/",
    element:
  },
])

function App() {
  

  return (
    <>
      <Navbar />
      <Hero />
      <Features />
      <Products/>
      <WhyChooseUs />
      <AppointmentForm/>
      <Footer/>
    </>
  )
}

export default App
