"use client";
import { motion } from "motion/react";
import React from "react";
import { ImagesSlider } from "../components/ui/images-slider";

export function ImagesSliderDemo() {
   const images = [
    "https://media.istockphoto.com/id/1309085617/photo/happy-young-woman-and-man-hugging-using-smartphone-together.jpg?s=2048x2048&w=is&k=20&c=WmueAMgEFdFasW6m0aFGC9w2BE_vwL06Op2M0UvzZMI=",
    "https://media.istockphoto.com/id/2195198296/photo/a-modern-kitchen-with-green-cabinetry-a-marble-backsplash-stylish-pendant-lights-and-a-wooden.jpg?s=2048x2048&w=is&k=20&c=Mu4RYSU_tnBalWL_YAtDdMYCLMEVejsguDD8EjY0OQ4=",
    "https://media.istockphoto.com/id/1379386392/photo/modern-living-room-interior-with-beautiful-curtains-on-window.jpg?s=2048x2048&w=is&k=20&c=J8pvNbeVydu5N45gPDLxpIIpRKI0OqHMTBz3AAKVSFk=",
    "https://media.istockphoto.com/id/1136936444/photo/a-cozy-and-modern-coral-kitchen-room-interior-3d-render.jpg?s=1024x1024&w=is&k=20&c=zb2p48i4KM6n7em-Ijl-Xu9NR46WbOD1K9W7jXG6ecY=",
  ];
  return (
    <ImagesSlider className="h-[40rem]" images={images}>
      <motion.div
        initial={{
          opacity: 0,
          y: -80,
        }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          duration: 0.6,
        }}
        className="z-50 flex flex-col justify-center items-center">
        <h1
          className="relative z-10 mx-auto max-w-4xl text-center text-2xl font-bold text-white md:text-4xl lg:text-7xl dark:text-slate-300">
          {"Complete Home Elegance – Inside Every Stitch & Shade."
            .split(" ")
            .map((word, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, filter: "blur(4px)", y: 10 }}
                animate={{ opacity: 1, filter: "blur(0px)", y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.1,
                  ease: "easeInOut",
                }}
                className="mr-2 inline-block">
                {word}
              </motion.span>
            ))}
        </h1>
        <motion.p
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          transition={{
            duration: 0.3,
            delay: 0.8,
          }}
          className="relative z-10 mx-auto max-w-xl py-4 text-center text-lg font-normal text-gray-300 dark:text-neutral-400">
          vibrant wallpaper From elegant sofa upholstery to stylish modular kitchens, trendy curtains tos – we bring every corner of your home to life with customized comfort and design.
        </motion.p>
       <button className="bg-gradient-to-r from-teal-400 to-blue-500 hover:from-blue-500 hover:to-teal-400 text-white px-4 py-2 rounded-full font-semibold shadow-md transition duration-300">
              Book an Appointment
            </button>
      </motion.div>
    </ImagesSlider>
  );
}
