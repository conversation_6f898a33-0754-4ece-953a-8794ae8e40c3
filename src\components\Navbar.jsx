import React, { useState, useEffect } from "react";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Scroll handler to check if page is scrolled more than 50px
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Cleanup
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed w-full top-0 left-0 z-50 shadow-md transition-colors  ${
        scrolled ? "bg-black bg-opacity-90 text-white" : "bg-transparent text-white shadow-md border-b-2 border-gray-300"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo Section */}
          <div className="flex-shrink-0 flex items-center">
            <a href="/" className="ml-3 text-xl font-bold">
              Vasu Interio
            </a>
          </div>

          {/* Desktop Menu */}
          {/* <div className="hidden md:flex md:items-center space-x-3 lg:space-x-6 ">
            <a
              href="/modular-kitchen"
              className=" hover:text-blue-600 lg:font-medium "
            >
              Modular-kitchen
            </a>
            <a
              href="/upholstery"
              className=" hover:text-blue-600 lg:font-medium "
            >
              Upholstery
            </a>
            <a
              href="/curtain-&-blinds"
              className=" hover:text-blue-600 lg:font-medium "
            >
              Curtain & Blinds
            </a>
            <a
              href="/about"
              className=" hover:text-blue-600 lg:font-medium "
            >
              About
            </a>
            
            <button className="bg-gradient-to-r from-teal-400 to-blue-500 hover:from-blue-500 hover:to-teal-400 text-white px-4 py-2 rounded-full font-semibold shadow-md transition duration-300">
              Book an Appointment
            </button>
          </div> */}

          {/* Mobile menu button */}
          <div className="">
            <button
              onClick={() => setIsOpen(!isOpen)}
              type="button"
              className={`inline-flex items-center justify-center p-2 rounded-md hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white scrolled ? "bg-black bg-opacity-90" : "bg-white shadow-md border-b-2 border-gray-300 text-black"`}
              aria-controls="mobile-menu"
              aria-expanded={isOpen}
            >
              <span className="sr-only">Open main menu</span>
              {!isOpen ? (
                /* Hamburger Icon */
                <svg
                  className="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              ) : (
                /* Close Icon */
                <svg
                  className="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white rounded-b-lg shadow-md ">
            <a
              href="/modular-kitchen"
              className="block px-3 py-2 rounded-md text-base font-medium text-black"
              onClick={() => setIsOpen(false)}
            >
              Modular Kitchen
            </a>
            <a
              href="/upholstery"
              className="block px-3 py-2 rounded-md text-base font-medium text-black"
              onClick={() => setIsOpen(false)}
            >
              Upholstery
            </a>
            <a
              href="/curtains-&-blinds"
              className="block px-3 py-2 rounded-md text-base font-medium text-black"
              onClick={() => setIsOpen(false)}
            >
              Curtains & Blinds
            </a>
            <a
              href="#about"
              className="block px-3 py-2 rounded-md text-base font-medium text-black"
              onClick={() => setIsOpen(false)}
            >
              About
            </a>
            <button
              className="w-full text-left px-3 py-2 rounded-md bg-gradient-to-r from-teal-400 to-blue-500 hover:from-blue-500 hover:to-teal-400 text-white font-semibold shadow-md transition duration-300"
              onClick={() => setIsOpen(false)}
            >
              Book an Appointment
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
