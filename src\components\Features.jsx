import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"

import data from "@/data.json"
import Autoplay from "embla-carousel-autoplay"

const Features = () => {

  
  return (
    <div className='flex-grow flex flex-col items-center justify-center px-4 md:px-24 py-12 bg-gray-200'>
      <section className='text-center mb- md:mb'>
        <h1 className='text-3xl md:text-5xl font-bold'>Ready to give your home a makeover?</h1>
        <p className='mt-3 md:mt-4 text-base md:text-lg'>Explore our interior design solutions</p>
      </section>
      <Carousel
  plugins={[Autoplay({ delay: 3000 })]}
  className="w-full max-w-6xl h-full"
>
  <CarouselContent>
    {data.map((data, index) => (
      <CarouselItem key={index}>
        <div className="p-2">
          <Card className="relative overflow-hidden rounded-xl shadow-lg">
            {/* Image */}
            
              <img
                src={data.img}
                alt={data.name}
                className="w-full h-[200px] md:h-[300px] lg:h-[600px] object-cover"
              />
            

            {/* Text Overlay */}
            <div className="  absolute bottom-0 left-0 w-full bg-white/30 pb-2 lg:p-4 text-center">
              <h1 className=" text-lg md:text-2xl lg:text-4xl font-bold text-gray-800">{data.name}</h1>
              <p className=" md:mt-2 text-sm md:text-base lg:text-lg text-gray-700">{data.description}</p>
            </div>
          </Card>
        </div>
      </CarouselItem>
    ))}
  </CarouselContent>

  
</Carousel>

    </div>
  )
}

export default Features